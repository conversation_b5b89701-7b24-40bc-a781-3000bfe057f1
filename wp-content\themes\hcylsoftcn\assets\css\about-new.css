/* 重新设计的关于我们页面样式 */

/* 强制重置所有可能冲突的样式 */
.about-page-isolate {
  all: initial !important;
  display: block !important;
  width: 100% !important;
  position: relative !important;
  z-index: 1 !important;
}

.wrapper,
.wrapper-pad,
.tabs-content,
.about-content-main,
.about-company-box,
.about-company,
.about-bg-img,
.about-text {
  all: unset !important;
}

/* 基础样式重置 */
.about-page-wrapper * {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.about-page-wrapper {
  all: initial !important;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif !important;
  line-height: 1.6 !important;
  color: #333 !important;
  width: 100% !important;
  min-height: 100vh !important;
  overflow-x: hidden !important;
  display: block !important;
  position: relative !important;
  z-index: 1 !important;
  background: #fff !important;
  margin: 0 !important;
  padding: 0 !important;
}

.about-page-wrapper .container {
  max-width: 1200px !important;
  margin: 0 auto !important;
  padding: 0 20px !important;
  width: 100% !important;
  display: block !important;
}

/* 通用区域标题样式 */
.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 10px;
  position: relative;
}

.section-title::after {
  content: "";
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background: linear-gradient(90deg, #4f7fe8, #97b5f7);
  border-radius: 2px;
}

.section-subtitle {
  font-size: 1.1rem;
  color: #7f8c8d;
  text-transform: uppercase;
  letter-spacing: 2px;
  margin-top: 20px;
}

/* 英雄区域 */
.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 100px 0;
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  z-index: 2;
}

.hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 800;
  margin-bottom: 20px;
  background: linear-gradient(45deg, #fff, #e3f2fd);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: 1.5rem;
  margin-bottom: 15px;
  color: #e3f2fd;
}

.hero-description {
  font-size: 1.2rem;
  margin-bottom: 30px;
  color: #f8f9fa;
  opacity: 0.9;
}

.hero-line {
  width: 120px;
  height: 4px;
  background: linear-gradient(90deg, #fff, #e3f2fd);
  border-radius: 2px;
}

.hero-image {
  text-align: center;
}

.hero-image img {
  max-width: 100%;
  height: auto;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

/* 公司简介区域 */
.company-intro-section {
  padding: 100px 0;
  background: #f8f9fa;
}

.intro-content {
  max-width: 800px;
  margin: 0 auto;
}

.intro-text {
  font-size: 1.1rem;
  line-height: 1.8;
  color: #555;
  text-align: justify;
  background: white;
  padding: 40px;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* 业务板块区域 */
.business-section {
  padding: 100px 0;
  background: white;
}

.business-image {
  text-align: center;
  margin-top: 40px;
}

.business-image img {
  max-width: 100%;
  height: auto;
  border-radius: 15px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

/* 核心优势区域 */
.advantages-section {
  padding: 100px 0;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.advantages-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 40px;
  margin-top: 60px;
}

.advantage-card {
  background: white;
  padding: 40px 30px;
  border-radius: 20px;
  text-align: center;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
}

.advantage-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #4f7fe8, #97b5f7);
}

.advantage-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.advantage-icon {
  margin-bottom: 25px;
}

.advantage-icon img {
  width: 80px;
  height: 80px;
  object-fit: contain;
}

.advantage-title {
  font-size: 1.4rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 20px;
}

.advantage-description {
  color: #666;
  line-height: 1.7;
  text-align: left;
}

/* 团队介绍区域 */
.team-section {
  padding: 100px 0;
  background: white;
}

.team-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: start;
}

.team-description {
  font-size: 1.1rem;
  line-height: 1.8;
  color: #555;
}

.team-description p {
  margin-bottom: 20px;
}

.gallery-header {
  margin-bottom: 30px;
}

.gallery-header h3 {
  font-size: 1.8rem;
  color: #2c3e50;
  margin-bottom: 10px;
}

.gallery-header p {
  color: #7f8c8d;
  font-size: 1rem;
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
}

.gallery-item {
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.gallery-item.large {
  grid-column: span 2;
  grid-row: span 2;
}

.gallery-item:hover {
  transform: scale(1.05);
}

.gallery-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 公司资质区域 */
.qualifications-section {
  padding: 100px 0;
  background: #f8f9fa;
}

.qualifications-content {
  background: white;
  padding: 40px;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* 发展历程区域 */
.history-section {
  padding: 100px 0;
  background: white;
}

.history-content {
  background: #f8f9fa;
  padding: 40px;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-content {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .section-title {
    font-size: 2rem;
  }

  .advantages-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .team-content {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .gallery-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .gallery-item.large {
    grid-column: span 2;
    grid-row: span 1;
  }

  .container {
    padding: 0 15px;
  }

  .intro-text,
  .qualifications-content,
  .history-content {
    padding: 30px 20px;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 2rem;
  }

  .section-title {
    font-size: 1.8rem;
  }

  .gallery-grid {
    grid-template-columns: 1fr;
  }

  .gallery-item.large {
    grid-column: span 1;
  }
}

/* 隐藏类 */
.pc-pad {
  display: block;
}

.mb-only {
  display: none;
}

@media (max-width: 768px) {
  .pc-pad {
    display: none;
  }

  .mb-only {
    display: block;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.section-header,
.advantage-card,
.intro-text {
  animation: fadeInUp 0.6s ease-out;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(90deg, #4f7fe8, #97b5f7);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(90deg, #3d6bd7, #7ba3f4);
}
