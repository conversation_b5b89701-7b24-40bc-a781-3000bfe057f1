<?php

/**
 * 重新设计的关于我们页面模板
 *
 * @package hcylsoftcn
 */
?>

<!-- 内联样式确保新设计能够正确显示 -->
<style>
    /* 强制重置所有可能冲突的样式 */
    .wrapper,
    .wrapper-pad,
    .tabs-content,
    .about-content-main,
    .about-company-box,
    .about-company,
    .about-bg-img,
    .about-text {
        all: unset !important;
    }

    /* 确保about-page-wrapper完全独立 */
    .about-page-wrapper {
        all: initial !important;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
        line-height: 1.6 !important;
        color: #333 !important;
        width: 100% !important;
        min-height: 100vh !important;
        overflow-x: hidden !important;
        display: block !important;
        position: relative !important;
        z-index: 1 !important;
        background: #fff !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    .about-page-wrapper *,
    .about-page-wrapper *::before,
    .about-page-wrapper *::after {
        margin: 0 !important;
        padding: 0 !important;
        box-sizing: border-box !important;
        font-family: inherit !important;
    }

    .about-page-wrapper .container {
        max-width: 1200px !important;
        margin: 0 auto !important;
        padding: 0 20px !important;
        width: 100% !important;
        display: block !important;
    }

    .about-page-wrapper .section-header {
        text-align: center !important;
        margin-bottom: 60px !important;
        display: block !important;
    }

    .about-page-wrapper .section-title {
        font-size: 2.5rem !important;
        font-weight: 700 !important;
        color: #2c3e50 !important;
        margin-bottom: 10px !important;
        position: relative !important;
        display: block !important;
        text-align: center !important;
    }

    .about-page-wrapper .section-title::after {
        content: '' !important;
        position: absolute !important;
        bottom: -10px !important;
        left: 50% !important;
        transform: translateX(-50%) !important;
        width: 60px !important;
        height: 4px !important;
        background: linear-gradient(90deg, #4f7fe8, #97b5f7) !important;
        border-radius: 2px !important;
        display: block !important;
    }

    .about-page-wrapper .section-subtitle {
        font-size: 1.1rem !important;
        color: #7f8c8d !important;
        text-transform: uppercase !important;
        letter-spacing: 2px !important;
        margin-top: 20px !important;
        display: block !important;
        text-align: center !important;
    }

    .about-page-wrapper .hero-section {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        color: white !important;
        padding: 100px 0 !important;
        position: relative !important;
        overflow: hidden !important;
        display: block !important;
        width: 100% !important;
    }

    .about-page-wrapper .hero-container {
        max-width: 1200px !important;
        margin: 0 auto !important;
        padding: 0 20px !important;
        position: relative !important;
        z-index: 2 !important;
        display: block !important;
        width: 100% !important;
    }

    .about-page-wrapper .hero-content {
        display: grid !important;
        grid-template-columns: 1fr 1fr !important;
        gap: 60px !important;
        align-items: center !important;
        width: 100% !important;
    }

    .about-page-wrapper .hero-title {
        font-size: 3.5rem !important;
        font-weight: 800 !important;
        margin-bottom: 20px !important;
        color: white !important;
        display: block !important;
    }

    .about-page-wrapper .hero-subtitle {
        font-size: 1.5rem !important;
        margin-bottom: 15px !important;
        color: #e3f2fd !important;
        display: block !important;
    }

    .about-page-wrapper .hero-description {
        font-size: 1.2rem !important;
        margin-bottom: 30px !important;
        color: #f8f9fa !important;
        opacity: 0.9 !important;
        display: block !important;
    }

    .about-page-wrapper .hero-line {
        width: 120px !important;
        height: 4px !important;
        background: linear-gradient(90deg, #fff, #e3f2fd) !important;
        border-radius: 2px !important;
        display: block !important;
    }

    .about-page-wrapper .hero-image {
        text-align: center !important;
        display: block !important;
    }

    .about-page-wrapper .hero-image img {
        max-width: 100% !important;
        height: auto !important;
        border-radius: 20px !important;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2) !important;
        display: block !important;
    }

    .about-page-wrapper .company-intro-section {
        padding: 100px 0 !important;
        background: #f8f9fa !important;
        display: block !important;
        width: 100% !important;
    }

    .about-page-wrapper .intro-content {
        max-width: 800px !important;
        margin: 0 auto !important;
        display: block !important;
        width: 100% !important;
    }

    .about-page-wrapper .intro-text {
        font-size: 1.1rem !important;
        line-height: 1.8 !important;
        color: #555 !important;
        text-align: justify !important;
        background: white !important;
        padding: 40px !important;
        border-radius: 15px !important;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1) !important;
        display: block !important;
        width: 100% !important;
    }

    .about-page-wrapper .business-section {
        padding: 100px 0 !important;
        background: white !important;
        display: block !important;
        width: 100% !important;
    }

    .about-page-wrapper .business-image {
        text-align: center !important;
        margin-top: 40px !important;
        display: block !important;
    }

    .about-page-wrapper .business-image img {
        max-width: 100% !important;
        height: auto !important;
        border-radius: 15px !important;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1) !important;
        display: block !important;
    }

    .about-page-wrapper .advantages-section {
        padding: 100px 0 !important;
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%) !important;
        display: block !important;
        width: 100% !important;
    }

    .about-page-wrapper .advantages-grid {
        display: grid !important;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)) !important;
        gap: 40px !important;
        margin-top: 60px !important;
        width: 100% !important;
    }

    .about-page-wrapper .advantage-card {
        background: white !important;
        padding: 40px 30px !important;
        border-radius: 20px !important;
        text-align: center !important;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1) !important;
        transition: transform 0.3s ease, box-shadow 0.3s ease !important;
        position: relative !important;
        overflow: hidden !important;
        display: block !important;
    }

    .about-page-wrapper .advantage-card::before {
        content: '' !important;
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        height: 4px !important;
        background: linear-gradient(90deg, #4f7fe8, #97b5f7) !important;
        display: block !important;
    }

    .about-page-wrapper .advantage-card:hover {
        transform: translateY(-10px) !important;
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15) !important;
    }

    .about-page-wrapper .advantage-icon {
        margin-bottom: 25px !important;
        display: block !important;
    }

    .about-page-wrapper .advantage-icon img {
        width: 80px !important;
        height: 80px !important;
        object-fit: contain !important;
        display: block !important;
        margin: 0 auto !important;
    }

    .about-page-wrapper .advantage-title {
        font-size: 1.4rem !important;
        font-weight: 600 !important;
        color: #2c3e50 !important;
        margin-bottom: 20px !important;
        display: block !important;
    }

    .about-page-wrapper .advantage-description {
        color: #666 !important;
        line-height: 1.7 !important;
        text-align: left !important;
        display: block !important;
    }

    @media (max-width: 768px) {
        .about-page-wrapper .hero-content {
            grid-template-columns: 1fr !important;
            gap: 40px !important;
            text-align: center !important;
        }

        .about-page-wrapper .hero-title {
            font-size: 2.5rem !important;
        }

        .about-page-wrapper .section-title {
            font-size: 2rem !important;
        }

        .about-page-wrapper .advantages-grid {
            grid-template-columns: 1fr !important;
            gap: 30px !important;
        }

        .about-page-wrapper .container {
            padding: 0 15px !important;
        }
    }

    .about-page-wrapper .pc-pad {
        display: block !important;
    }

    .about-page-wrapper .mb-only {
        display: none !important;
    }

    @media (max-width: 768px) {
        .about-page-wrapper .pc-pad {
            display: none !important;
        }

        .about-page-wrapper .mb-only {
            display: block !important;
        }
    }
</style>

<!-- 完全隔离的关于我们页面容器 -->
<div class="about-page-isolate" style="all: initial; display: block; width: 100%; position: relative; z-index: 1;">
    <div class="about-page-wrapper">
        <!-- 英雄区域 -->
        <section class="hero-section">
            <div class="hero-container">
                <div class="hero-content">
                    <div class="hero-text">
                        <h1 class="hero-title">华创云联</h1>
                        <p class="hero-subtitle">围绕客户需求持续创新</p>
                        <p class="hero-description">国内领先的软件基础平台解决方案与产品提供商</p>
                        <div class="hero-line"></div>
                    </div>
                    <div class="hero-image">
                        <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/about/about-c.png" alt="华创云联" class="pc-pad" />
                        <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/app/about/about-c.png" alt="华创云联" class="mb-only" />
                    </div>
                </div>
            </div>
        </section>

        <!-- 公司简介区域 -->
        <section class="company-intro-section">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">公司简介</h2>
                    <p class="section-subtitle">Company Profile</p>
                </div>
                <div class="intro-content">
                    <div class="intro-text">
                        <?php echo get_theme_mod('companyprofile'); ?>
                    </div>
                </div>
            </div>
        </section>

        <!-- 业务板块区域 -->
        <section class="business-section">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">公司发展三大业务板块</h2>
                    <p class="section-subtitle">THREE MAJOR BUSINESSES</p>
                </div>
                <div class="business-image">
                    <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/app/about/business.png" alt="业务板块" />
                </div>
            </div>
        </section>

        <!-- 核心优势区域 -->
        <section class="advantages-section">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">核心优势</h2>
                    <p class="section-subtitle">Core Advantages</p>
                </div>
                <div class="advantages-grid">
                    <div class="advantage-card">
                        <div class="advantage-icon">
                            <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/about/com-1.png" alt="高效团队" />
                        </div>
                        <h3 class="advantage-title">高效的技术团队</h3>
                        <p class="advantage-description">
                            公司拥有众多经验丰富的软件开发工程师、系统维护工程师，他们曾任职于国内知名企业上市公司软件开发中心，长期提供人力外派驻场开发服务。
                        </p>
                    </div>
                    <div class="advantage-card">
                        <div class="advantage-icon">
                            <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/about/com-2.png" alt="行业经验" />
                        </div>
                        <h3 class="advantage-title">资深的行业经验</h3>
                        <p class="advantage-description">
                            公司与国内多家知名软件集成商保持着长期稳定的合作关系，客户主要为中大型企事业单位，在北京服务于多家央企，持续稳定为客户提供技术开发与支持服务。
                        </p>
                    </div>
                    <div class="advantage-card">
                        <div class="advantage-icon">
                            <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/about/com-3.png" alt="合作关系" />
                        </div>
                        <h3 class="advantage-title">良好的合作关系</h3>
                        <p class="advantage-description">
                            从立项初期即时刻关注客户满意度，及时反馈和解决问题。与客户建立长久良好的合作关系，共同应对工作中面临的各项挑战。
                        </p>
                    </div>
                </div>
            </div>
        </section>
    </div>
</div>